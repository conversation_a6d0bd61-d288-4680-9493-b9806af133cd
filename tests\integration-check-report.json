[{"id": "INT-001", "description": "All imports are properly maintained", "passed": true}, {"id": "INT-002", "description": "Core Pro validation functions are exported", "passed": true}, {"id": "INT-003", "description": "Manager dependencies are intact", "passed": true}, {"id": "INT-004", "description": "Storage patterns are preserved", "passed": true}, {"id": "INT-005", "description": "Error handling is maintained", "passed": true}, {"id": "INT-006", "description": "UI event handlers are connected", "passed": true}, {"id": "INT-007", "description": "Background script integration intact", "passed": true}, {"id": "INT-008", "description": "Manifest and configuration files unchanged", "passed": [{"matches": ["<all_urls>"], "js": ["content.js"], "css": ["content.css"]}]}, {"id": "INT-009", "description": "No broken function calls or references", "passed": true}, {"id": "INT-010", "description": "All modified files have consistent structure", "passed": false}]