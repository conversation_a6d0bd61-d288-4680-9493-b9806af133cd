/**
 * Auto Send Manager
 * Handles automatic sending of analysis results to integrations
 */
import { BaseManager } from '../core/BaseManager.js';
import { checkProStatus } from '../../../js/user/proStatus.js';
import { 
    getTelegramAutoSendSettings,
    updateTelegramAutoSendFailureCount,
    updateTelegramAutoSendSuccess,
    getTelegramSettings
} from '../../../js/user/telegramSettings.js';
import { 
    getDiscordAutoSendSettings,
    updateDiscordAutoSendFailureCount,
    updateDiscordAutoSendSuccess,
    getDiscordSettings
} from '../../../js/user/discordSettings.js';
import { sendAnalysisToTelegram } from '../../../js/integrations/telegram.js';
import { sendAnalysisToDiscord } from '../../../js/integrations/discord.js';

export class AutoSendManager extends BaseManager {
    constructor(controller) {
        super(controller);
    }

    async init() {
        await super.init();
    }

    /**
     * Handle auto-send after successful analysis
     */
    async handleAutoSend(analysisType, result) {
        try {
            // MODIFIED: Pro check removed - auto-send always accessible
            const proStatus = await checkProStatus();
            // Continue with auto-send (no Pro validation required)

            // Get auto-send settings for both integrations
            const [telegramAutoSend, discordAutoSend] = await Promise.all([
                getTelegramAutoSendSettings(),
                getDiscordAutoSendSettings()
            ]);

            // Check if any auto-send is enabled
            if (!telegramAutoSend.enabled && !discordAutoSend.enabled) {
                return; // No auto-send enabled
            }

            // Prepare analysis data for sending
            const analysisData = {
                analysisType: analysisType,
                result: result,
                date: new Date()
            };

            // Handle auto-sends in parallel
            const autoSendPromises = [];

            if (telegramAutoSend.enabled) {
                autoSendPromises.push(this.handleTelegramAutoSend(analysisData));
            }

            if (discordAutoSend.enabled) {
                autoSendPromises.push(this.handleDiscordAutoSend(analysisData));
            }

            // Execute auto-sends
            if (autoSendPromises.length > 0) {
                await Promise.allSettled(autoSendPromises);
            }

        } catch (error) {
            console.error('Error in auto-send handler:', error);
            // Don't show error to user for auto-send failures to avoid disrupting the main flow
        }
    }

    /**
     * Handle Telegram auto-send
     */
    async handleTelegramAutoSend(analysisData) {
        try {
            const settings = await getTelegramSettings();
            if (!settings || !settings.botToken || !settings.chatId) {
                throw new Error('Telegram not configured');
            }

            const result = await sendAnalysisToTelegram(analysisData, settings.botToken, settings.chatId);
            
            if (result.success) {
                await updateTelegramAutoSendSuccess();
                console.log('Telegram auto-send successful');
            } else {
                throw new Error(result.error || 'Unknown error');
            }

        } catch (error) {
            console.error('Telegram auto-send failed:', error);
            
            // Update failure count
            const currentSettings = await getTelegramAutoSendSettings();
            const newFailureCount = (currentSettings.failureCount || 0) + 1;
            await updateTelegramAutoSendFailureCount(newFailureCount);
            
            // Show user notification for auto-disable
            if (newFailureCount >= 3) {
                this.controller.uiManager.showError('Telegram auto-send disabled due to repeated failures. Please check your settings.');
            }
        }
    }

    /**
     * Handle Discord auto-send
     */
    async handleDiscordAutoSend(analysisData) {
        try {
            const settings = await getDiscordSettings();
            if (!settings || !settings.webhookUrl) {
                throw new Error('Discord not configured');
            }

            const result = await sendAnalysisToDiscord(analysisData, settings.webhookUrl);
            
            if (result.success) {
                await updateDiscordAutoSendSuccess();
                console.log('Discord auto-send successful');
            } else {
                throw new Error(result.error || 'Unknown error');
            }

        } catch (error) {
            console.error('Discord auto-send failed:', error);
            
            // Update failure count
            const currentSettings = await getDiscordAutoSendSettings();
            const newFailureCount = (currentSettings.failureCount || 0) + 1;
            await updateDiscordAutoSendFailureCount(newFailureCount);
            
            // Show user notification for auto-disable
            if (newFailureCount >= 3) {
                this.controller.uiManager.showError('Discord auto-send disabled due to repeated failures. Please check your settings.');
            }
        }
    }
}
