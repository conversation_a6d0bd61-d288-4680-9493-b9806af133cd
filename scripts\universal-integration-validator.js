#!/usr/bin/env node

/**
 * Universal Integration Validator
 * Automates the integration checklist validation process
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class UniversalIntegrationValidator {
    constructor(projectPath = '.') {
        this.projectPath = projectPath;
        this.results = {
            discovery: [],
            structure: [],
            dependencies: [],
            safety: [],
            systems: [],
            validation: [],
            testing: [],
            feedback: [],
            verification: []
        };
    }

    log(phase, message, status = 'INFO') {
        const timestamp = new Date().toISOString();
        const statusIcon = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : status === 'WARN' ? '⚠️' : 'ℹ️';
        console.log(`${statusIcon} [${phase}] ${message}`);
        
        if (!this.results[phase.toLowerCase()]) {
            this.results[phase.toLowerCase()] = [];
        }
        this.results[phase.toLowerCase()].push({
            timestamp,
            message,
            status
        });
    }

    // Phase 1: Discovery & Mapping
    async runDiscoveryPhase(patterns = []) {
        this.log('DISCOVERY', 'Starting discovery and mapping phase...');
        
        try {
            // Check if all specified patterns exist
            for (const pattern of patterns) {
                try {
                    const result = execSync(`grep -r "${pattern}" . --include="*.js" --include="*.html" --include="*.css" --include="*.json" || true`, 
                        { encoding: 'utf8', cwd: this.projectPath });
                    
                    if (result.trim()) {
                        this.log('DISCOVERY', `Found pattern "${pattern}" in codebase`, 'PASS');
                    } else {
                        this.log('DISCOVERY', `Pattern "${pattern}" not found - may be fully removed`, 'WARN');
                    }
                } catch (error) {
                    this.log('DISCOVERY', `Error searching for pattern "${pattern}": ${error.message}`, 'FAIL');
                }
            }
            
            // Check file types coverage
            const fileTypes = ['.js', '.html', '.css', '.json', '.md'];
            for (const type of fileTypes) {
                const files = this.findFilesByExtension(type);
                this.log('DISCOVERY', `Found ${files.length} ${type} files`, files.length > 0 ? 'PASS' : 'WARN');
            }
            
        } catch (error) {
            this.log('DISCOVERY', `Discovery phase failed: ${error.message}`, 'FAIL');
        }
    }

    // Phase 2: System Structure Integration
    async runStructurePhase(modifiedFiles = []) {
        this.log('STRUCTURE', 'Validating system structure integration...');
        
        for (const file of modifiedFiles) {
            try {
                // Syntax validation
                if (file.endsWith('.js')) {
                    execSync(`node -c "${file}"`, { cwd: this.projectPath });
                    this.log('STRUCTURE', `Syntax valid: ${file}`, 'PASS');
                }
                
                // Check for common breaking patterns
                const content = fs.readFileSync(path.join(this.projectPath, file), 'utf8');
                
                if (content.includes('undefined.') || content.includes('null.')) {
                    this.log('STRUCTURE', `Potential null reference in ${file}`, 'WARN');
                }
                
                if (content.includes('import') || content.includes('export')) {
                    this.log('STRUCTURE', `Module structure maintained in ${file}`, 'PASS');
                }
                
            } catch (error) {
                this.log('STRUCTURE', `Structure validation failed for ${file}: ${error.message}`, 'FAIL');
            }
        }
    }

    // Phase 3: Dependencies & Connections
    async runDependenciesPhase() {
        this.log('DEPENDENCIES', 'Checking dependencies and connections...');
        
        try {
            // Check package.json if exists
            if (fs.existsSync(path.join(this.projectPath, 'package.json'))) {
                const pkg = JSON.parse(fs.readFileSync(path.join(this.projectPath, 'package.json'), 'utf8'));
                this.log('DEPENDENCIES', 'package.json is valid JSON', 'PASS');
            }
            
            // Check manifest.json if exists (for browser extensions)
            if (fs.existsSync(path.join(this.projectPath, 'manifest.json'))) {
                const manifest = JSON.parse(fs.readFileSync(path.join(this.projectPath, 'manifest.json'), 'utf8'));
                this.log('DEPENDENCIES', 'manifest.json is valid JSON', 'PASS');
            }
            
        } catch (error) {
            this.log('DEPENDENCIES', `Dependencies validation failed: ${error.message}`, 'FAIL');
        }
    }

    // Phase 4: Production Safety
    async runSafetyPhase() {
        this.log('SAFETY', 'Validating production safety...');
        
        try {
            // Check for common unsafe patterns
            const jsFiles = this.findFilesByExtension('.js');
            
            for (const file of jsFiles.slice(0, 10)) { // Limit to first 10 files for performance
                const content = fs.readFileSync(file, 'utf8');
                
                // Check for error handling
                if (content.includes('try') && content.includes('catch')) {
                    this.log('SAFETY', `Error handling present in ${path.basename(file)}`, 'PASS');
                } else if (content.length > 1000) { // Only warn for substantial files
                    this.log('SAFETY', `No error handling detected in ${path.basename(file)}`, 'WARN');
                }
            }
            
        } catch (error) {
            this.log('SAFETY', `Safety validation failed: ${error.message}`, 'FAIL');
        }
    }

    // Phase 5: Connected Systems
    async runSystemsPhase() {
        this.log('SYSTEMS', 'Validating connected systems...');
        
        try {
            // Check for HTML-JS connections
            if (fs.existsSync(path.join(this.projectPath, 'popup.html'))) {
                const html = fs.readFileSync(path.join(this.projectPath, 'popup.html'), 'utf8');
                const buttonIds = html.match(/id="([^"]+)"/g) || [];
                
                this.log('SYSTEMS', `Found ${buttonIds.length} HTML elements with IDs`, 'PASS');
                
                // Check if corresponding event handlers exist
                const jsFiles = this.findFilesByExtension('.js');
                let handlersFound = 0;
                
                for (const file of jsFiles) {
                    const content = fs.readFileSync(file, 'utf8');
                    for (const buttonId of buttonIds) {
                        const id = buttonId.match(/id="([^"]+)"/)[1];
                        if (content.includes(`getElementById('${id}')`) || content.includes(`getElementById("${id}")`)) {
                            handlersFound++;
                            break;
                        }
                    }
                }
                
                this.log('SYSTEMS', `Event handlers found for UI elements`, handlersFound > 0 ? 'PASS' : 'WARN');
            }
            
        } catch (error) {
            this.log('SYSTEMS', `Systems validation failed: ${error.message}`, 'FAIL');
        }
    }

    // Helper method to find files by extension
    findFilesByExtension(extension) {
        const files = [];
        
        function walkDir(dir) {
            try {
                const items = fs.readdirSync(dir);
                for (const item of items) {
                    const fullPath = path.join(dir, item);
                    const stat = fs.statSync(fullPath);
                    
                    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
                        walkDir(fullPath);
                    } else if (stat.isFile() && item.endsWith(extension)) {
                        files.push(fullPath);
                    }
                }
            } catch (error) {
                // Skip directories we can't read
            }
        }
        
        walkDir(this.projectPath);
        return files;
    }

    // Run all phases
    async runFullValidation(options = {}) {
        const {
            patterns = [],
            modifiedFiles = [],
            skipPhases = []
        } = options;

        console.log('🔍 Universal Integration Validation Starting...\n');

        if (!skipPhases.includes('discovery')) {
            await this.runDiscoveryPhase(patterns);
        }
        
        if (!skipPhases.includes('structure')) {
            await this.runStructurePhase(modifiedFiles);
        }
        
        if (!skipPhases.includes('dependencies')) {
            await this.runDependenciesPhase();
        }
        
        if (!skipPhases.includes('safety')) {
            await this.runSafetyPhase();
        }
        
        if (!skipPhases.includes('systems')) {
            await this.runSystemsPhase();
        }

        this.generateReport();
    }

    // Generate comprehensive report
    generateReport() {
        console.log('\n' + '='.repeat(60));
        console.log('📊 UNIVERSAL INTEGRATION VALIDATION REPORT');
        console.log('='.repeat(60));

        let totalChecks = 0;
        let passedChecks = 0;
        let failedChecks = 0;
        let warnings = 0;

        for (const [phase, results] of Object.entries(this.results)) {
            if (results.length === 0) continue;

            console.log(`\n🔍 ${phase.toUpperCase()} PHASE:`);
            
            for (const result of results) {
                totalChecks++;
                if (result.status === 'PASS') passedChecks++;
                else if (result.status === 'FAIL') failedChecks++;
                else if (result.status === 'WARN') warnings++;
            }
        }

        console.log(`\n📈 SUMMARY:`);
        console.log(`Total Checks: ${totalChecks}`);
        console.log(`✅ Passed: ${passedChecks}`);
        console.log(`❌ Failed: ${failedChecks}`);
        console.log(`⚠️ Warnings: ${warnings}`);
        console.log(`Success Rate: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

        if (failedChecks === 0) {
            console.log('\n🎉 Integration validation completed successfully!');
            console.log('✨ System appears ready for production deployment.');
        } else {
            console.log('\n⚠️ Integration issues detected.');
            console.log('🔧 Please address failed checks before deployment.');
        }

        // Save detailed report
        const reportPath = path.join(this.projectPath, 'integration-validation-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
        console.log(`\n📄 Detailed report saved to: ${reportPath}`);

        return failedChecks === 0;
    }
}

// CLI usage
if (require.main === module) {
    const validator = new UniversalIntegrationValidator();
    
    // Example usage for Pro gating removal
    validator.runFullValidation({
        patterns: ['isPro', 'Pro feature', 'pro-badge', 'Upgrade to Pro'],
        modifiedFiles: [
            'js/auth/proValidator.js',
            'js/user/proStatus.js',
            'js/popup/settings/SettingsManager.js',
            'popup.html'
        ]
    }).then(success => {
        process.exit(success ? 0 : 1);
    });
}

module.exports = { UniversalIntegrationValidator };
