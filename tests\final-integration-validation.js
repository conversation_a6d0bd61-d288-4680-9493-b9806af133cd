#!/usr/bin/env node

// Final Integration Validation - Simple and Accurate
// Validates that the Pro gating disable implementation is fully integrated

const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔍 Final Integration Validation\n');

const validationSteps = [
    {
        id: 'STEP-001',
        description: 'All modified files have valid syntax',
        check: () => {
            const files = [
                'js/auth/proValidator.js',
                'js/user/proStatus.js', 
                'js/popup/settings/SettingsManager.js',
                'js/popup/prompts/PromptUIManager.js',
                'js/popup/analysis/AnalysisManager.js',
                'js/popup/integrations/AutoSendManager.js',
                'js/popup/ui/UIManager.js'
            ];
            
            try {
                files.forEach(file => {
                    execSync(`node -c "${file}"`, { stdio: 'pipe' });
                });
                return true;
            } catch (error) {
                return false;
            }
        }
    },
    {
        id: 'STEP-002',
        description: 'Core Pro functions always return isPro: true',
        check: () => {
            const proValidator = fs.readFileSync('js/auth/proValidator.js', 'utf8');
            const proStatus = fs.readFileSync('js/user/proStatus.js', 'utf8');
            
            return proValidator.includes('isPro: true') && 
                   proStatus.includes('isPro: true') &&
                   proValidator.includes('All features available') &&
                   proStatus.includes('Pro gating disabled');
        }
    },
    {
        id: 'STEP-003',
        description: 'All Pro gates have been removed from managers',
        check: () => {
            const settingsManager = fs.readFileSync('js/popup/settings/SettingsManager.js', 'utf8');
            const promptManager = fs.readFileSync('js/popup/prompts/PromptUIManager.js', 'utf8');
            const analysisManager = fs.readFileSync('js/popup/analysis/AnalysisManager.js', 'utf8');
            const autoSendManager = fs.readFileSync('js/popup/integrations/AutoSendManager.js', 'utf8');
            
            // Check that Pro gating logic has been removed/modified
            const settingsOK = settingsManager.includes('Pro check removed') || 
                              !settingsManager.includes('Upgrade to Pro');
            const promptOK = promptManager.includes('Pro check removed');
            const analysisOK = analysisManager.includes('Pro check removed');
            const autoSendOK = autoSendManager.includes('Pro check removed');
            
            return settingsOK && promptOK && analysisOK && autoSendOK;
        }
    },
    {
        id: 'STEP-004',
        description: 'UI displays features as available to all users',
        check: () => {
            const uiManager = fs.readFileSync('js/popup/ui/UIManager.js', 'utf8');
            const settingsManager = fs.readFileSync('js/popup/settings/SettingsManager.js', 'utf8');
            
            return uiManager.includes('All Features Available') &&
                   settingsManager.includes('All Features Available');
        }
    },
    {
        id: 'STEP-005',
        description: 'Existing Pro user data structures preserved',
        check: () => {
            const proValidator = fs.readFileSync('js/auth/proValidator.js', 'utf8');
            const proStatus = fs.readFileSync('js/user/proStatus.js', 'utf8');
            
            return proValidator.includes('membershipDetails') &&
                   proValidator.includes('permanent') &&
                   proStatus.includes('membershipDetails') &&
                   proStatus.includes('hustleProKey') &&
                   proStatus.includes('hustleProStatus');
        }
    },
    {
        id: 'STEP-006',
        description: 'Chrome storage patterns maintained',
        check: () => {
            const proStatus = fs.readFileSync('js/user/proStatus.js', 'utf8');
            
            return proStatus.includes('chrome.storage.sync') &&
                   proStatus.includes('chrome.storage.sync.get') &&
                   proStatus.includes('chrome.storage.sync.set');
        }
    },
    {
        id: 'STEP-007',
        description: 'No breaking changes to function signatures',
        check: () => {
            const proValidator = fs.readFileSync('js/auth/proValidator.js', 'utf8');
            const proStatus = fs.readFileSync('js/user/proStatus.js', 'utf8');
            
            return proValidator.includes('export async function validateProKey(userKey, forceValidation = false)') &&
                   proStatus.includes('export async function checkProStatus()') &&
                   proStatus.includes('export async function setProKey(proKey)') &&
                   proStatus.includes('export async function refreshProStatus()');
        }
    }
];

function runFinalValidation() {
    let allPassed = true;
    const results = [];

    console.log('✅ Running Final Integration Validation...');
    console.log('─'.repeat(60));

    validationSteps.forEach(step => {
        try {
            const passed = step.check();
            const status = passed ? '✅' : '❌';
            
            console.log(`${status} ${step.id}: ${step.description}`);
            results.push({ ...step, passed });
            
            if (!passed) allPassed = false;
        } catch (error) {
            console.log(`❌ ${step.id}: ${step.description} (Error: ${error.message})`);
            results.push({ ...step, passed: false, error: error.message });
            allPassed = false;
        }
    });

    // Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 FINAL INTEGRATION VALIDATION SUMMARY');
    console.log('='.repeat(60));

    const totalSteps = validationSteps.length;
    const passedSteps = results.filter(r => r.passed).length;

    console.log(`Total Validation Steps: ${totalSteps}`);
    console.log(`Passed: ${passedSteps}`);
    console.log(`Failed: ${totalSteps - passedSteps}`);
    console.log(`Success Rate: ${((passedSteps / totalSteps) * 100).toFixed(1)}%`);

    if (allPassed) {
        console.log('\n🎉 FINAL INTEGRATION VALIDATION PASSED!');
        console.log('✨ Pro gating disable implementation is fully integrated.');
        console.log('🚀 System is production-ready with no breaking changes.');
        console.log('💯 All features are accessible to all users.');
        console.log('🔒 Existing Pro users continue to work seamlessly.');
    } else {
        console.log('\n❌ Some validation steps failed.');
        console.log('⚠️ Please review and address issues before deployment.');
    }

    return allPassed;
}

// Run validation if this script is executed directly
if (require.main === module) {
    const success = runFinalValidation();
    process.exit(success ? 0 : 1);
}

module.exports = { runFinalValidation, validationSteps };
